import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { getTenantId } from '../../../lib/auth-helpers'

// Secure server-side Supabase client
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

// GET /api/homepage/products - Get products for homepage
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    const tenantId = getTenantId(request)

    // Fetch products - filtered by tenant and active
    const { data: products, error } = await supabaseAdmin
      .from('products')
      .select(`
        *,
        packages(*)
      `)
      .eq('tenant_id', tenantId)
      .eq('active', true)
      .order('created_at', { ascending: false })
      .limit(12) // First 12 for homepage

    if (error) {
      throw error
    }

    return NextResponse.json({
      success: true,
      data: products || []
    })

  } catch (error) {
    console.error('Homepage products API error:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في تحميل المنتجات'
    }, { status: 500 })
  }
}
