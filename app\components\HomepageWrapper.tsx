"use client"

import { useEffect, useState } from "react"
import AdminSetup from "./AdminSetup"
import Tenant<PERSON>witcher from "./TenantSwitcher"
import { useAuth } from "../contexts/AuthContext"
import { cache } from "../lib/cache"

interface HomepageWrapperProps {
  children: React.ReactNode
  initialProducts: any[]
  initialCategories?: any[]
  initialHomepageSections?: any[]
  tenant: any
}

/**
 * Client wrapper for homepage - handles hydration and client-side features
 * while keeping the main page as SSR for better SEO
 */
export default function HomepageWrapper({
  children,
  initialProducts,
  initialCategories = [],
  initialHomepageSections = [],
  tenant
}: HomepageWrapperProps) {
  // Simple placeholders since DataContext was simplified
  const [products, setProducts] = useState<any[]>([])
  const [hydrated, setHydrated] = useState(false)
  const [isFromCache, setIsFromCache] = useState(false)
  const { user: currentUser } = useAuth()

  // Hydrate client-side data with SSR data and cache it (run only once)
  useEffect(() => {
    if (!hydrated) {
      // Check if we have cached data first (for subsequent visits)
      const cachedData = cache.get('homepage_data')

      if (cachedData && !initialProducts?.length) {
        // Use cached data if no fresh SSR data
        console.log('📦 Using cached homepage data')
        setProducts(cachedData.products || [])
        setIsFromCache(true)
        setHydrated(true)
        return
      }

      if (initialProducts && initialProducts.length > 0) {
        // Map products to the expected format
        const mappedProducts = initialProducts.map(product => ({
          id: product.id,
          title: product.title,
          slug: product.slug,
          description: product.description,
          cover_image: product.cover_image,
          category_id: product.category_id,
          featured: product.featured,
          original_price: product.original_price,
          user_price: product.user_price,
          discount_price: product.discount_price,
          distributor_price: product.distributor_price,
          created_at: product.created_at,
          updated_at: product.updated_at,
          tenant_id: product.tenant_id,
          categories: product.categories,
          packages: product.packages || [],
          custom_fields: product.custom_fields || [],
          dropdowns: product.dropdowns || []
        }))

        setProducts(mappedProducts)

        // Cache the SSR data for future visits
        cache.set('homepage_data', {
          products: mappedProducts,
          categories: initialCategories,
          sections: initialHomepageSections
        })

        console.log('✅ Client: Hydrated products from SSR:', mappedProducts.length)
        console.log('💾 Homepage data cached for future visits')
      }

      if (initialCategories && initialCategories.length > 0) {
        console.log('✅ Client: Categories available from SSR:', initialCategories.length)
      }

      if (initialHomepageSections && initialHomepageSections.length > 0) {
        console.log('✅ Client: Hydrated homepage sections from SSR:', initialHomepageSections.length)
      }

      setHydrated(true)
    }
  }, [initialProducts, initialCategories, initialHomepageSections, hydrated])

  // Add navigation detection for faster cache loading
  useEffect(() => {
    const handleRouteChange = () => {
      // When navigating to homepage, try to show cached content immediately
      if (window.location.pathname === '/') {
        const cachedData = cache.get('homepage_data')
        if (cachedData) {
          console.log('📦 Fast loading from cache on navigation')
          setIsFromCache(true)
        }
      }
    }

    // Listen for popstate (back/forward navigation)
    window.addEventListener('popstate', handleRouteChange)
    return () => window.removeEventListener('popstate', handleRouteChange)
  }, [])

  return (
    <div>
      {/* Cache indicator for development */}
      {process.env.NODE_ENV === 'development' && isFromCache && (
        <div className="fixed top-4 right-4 z-50 bg-green-600 text-white text-xs px-2 py-1 rounded shadow">
          📦 Loaded from cache
        </div>
      )}

      {/* Admin-only components */}
      {currentUser?.role === 'admin' && (
        <>
          <AdminSetup />
          <TenantSwitcher />
        </>
      )}

      {/* SSR content */}
      {children}
    </div>
  )
}
