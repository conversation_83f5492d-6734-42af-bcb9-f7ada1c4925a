"use client"

import { useState, useEffect } from 'react'
import { cache, CACHE_KEYS } from '../lib/cache'
import type { ProductWithCategory } from '../types'

interface UseProductsOptions {
  enableCache?: boolean
  cacheKey?: string
  refetchInterval?: number
}

interface UseProductsReturn {
  products: ProductWithCategory[]
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
  clearCache: () => void
}

export function useProducts(options: UseProductsOptions = {}): UseProductsReturn {
  const {
    enableCache = true,
    cacheKey = CACHE_KEYS.products,
    refetchInterval
  } = options

  const [products, setProducts] = useState<ProductWithCategory[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchProducts = async (useCache: boolean = true) => {
    try {
      setError(null)
      
      // Check cache first if enabled
      if (enableCache && useCache) {
        const cachedProducts = cache.get(cacheKey)
        if (cachedProducts) {
          console.log('📦 Using cached products data')
          setProducts(cachedProducts)
          setLoading(false)
          return
        }
      }

      console.log('🔄 Fetching fresh products data')
      setLoading(true)

      const response = await fetch('/api/admin/products', {
        credentials: 'include',
        headers: {
          'Cache-Control': 'no-cache'
        }
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()

      if (data.success) {
        const productsData = data.data || []
        setProducts(productsData)
        
        // Cache the data if caching is enabled
        if (enableCache) {
          cache.set(cacheKey, productsData)
          console.log('💾 Products data cached')
        }
      } else {
        throw new Error(data.error || 'فشل في تحميل المنتجات')
      }
    } catch (err) {
      console.error('Error fetching products:', err)
      const errorMessage = err instanceof Error ? err.message : 'حدث خطأ في تحميل المنتجات'
      setError(errorMessage)
      
      // If there's an error and we have cached data, use it
      if (enableCache) {
        const cachedProducts = cache.get(cacheKey)
        if (cachedProducts) {
          console.log('⚠️ Using cached data due to fetch error')
          setProducts(cachedProducts)
        }
      }
    } finally {
      setLoading(false)
    }
  }

  const refetch = async () => {
    await fetchProducts(false) // Force fresh fetch
  }

  const clearCache = () => {
    if (enableCache) {
      cache.remove(cacheKey)
      console.log('🗑️ Products cache cleared')
    }
  }

  // Initial fetch
  useEffect(() => {
    fetchProducts()
  }, []) // Only run once on mount

  // Optional refetch interval
  useEffect(() => {
    if (refetchInterval && refetchInterval > 0) {
      const interval = setInterval(() => {
        fetchProducts(false) // Force fresh fetch on interval
      }, refetchInterval)

      return () => clearInterval(interval)
    }
  }, [refetchInterval])

  return {
    products,
    loading,
    error,
    refetch,
    clearCache
  }
}

// Specialized hook for shop page
export function useShopProducts() {
  return useProducts({
    enableCache: true,
    cacheKey: 'shop_products',
    refetchInterval: 5 * 60 * 1000 // Refetch every 5 minutes
  })
}

// Specialized hook for admin products
export function useAdminProducts() {
  return useProducts({
    enableCache: true,
    cacheKey: 'admin_products',
    refetchInterval: 2 * 60 * 1000 // Refetch every 2 minutes for admin
  })
}
