"use client"

import { useState, useEffect, useCallback } from 'react'
import { cache, CACHE_KEYS } from '../lib/cache'
import type { Product, BannerSlide } from '../types'

interface HomepageData {
  products: Product[]
  banners: BannerSlide[]
  lastFetched: number
}

interface UseHomepageDataOptions {
  enableCache?: boolean
  cacheKey?: string
  initialData?: {
    products: Product[]
    banners: BannerSlide[]
  }
}

interface UseHomepageDataReturn {
  products: Product[]
  banners: BannerSlide[]
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
  clearCache: () => void
  isFromCache: boolean
}

export function useHomepageData(options: UseHomepageDataOptions = {}): UseHomepageDataReturn {
  const {
    enableCache = true,
    cacheKey = CACHE_KEYS.homepage,
    initialData
  } = options

  const [products, setProducts] = useState<Product[]>(initialData?.products || [])
  const [banners, setBanners] = useState<BannerSlide[]>(initialData?.banners || [])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isFromCache, setIsFromCache] = useState(false)

  const fetchHomepageData = useCallback(async (useCache: boolean = true) => {
    try {
      setError(null)
      
      // Check cache first if enabled and not initial load
      if (enableCache && useCache && !initialData) {
        const cachedData = cache.get(cacheKey) as HomepageData | null
        if (cachedData) {
          console.log('📦 Using cached homepage data')
          setProducts(cachedData.products)
          setBanners(cachedData.banners)
          setIsFromCache(true)
          setLoading(false)
          return
        }
      }

      // If we have initial data from SSR, use it and cache it
      if (initialData && !cache.get(cacheKey)) {
        console.log('💾 Caching initial SSR homepage data')
        const dataToCache: HomepageData = {
          products: initialData.products,
          banners: initialData.banners,
          lastFetched: Date.now()
        }
        cache.set(cacheKey, dataToCache)
        setProducts(initialData.products)
        setBanners(initialData.banners)
        setIsFromCache(false)
        return
      }

      // Fetch fresh data from API
      console.log('🔄 Fetching fresh homepage data from API')
      setLoading(true)
      setIsFromCache(false)

      const [productsResponse, bannersResponse] = await Promise.allSettled([
        fetch('/api/homepage/products', {
          credentials: 'include',
          headers: {
            'Cache-Control': 'no-cache'
          }
        }),
        fetch('/api/homepage/banners', {
          credentials: 'include',
          headers: {
            'Cache-Control': 'no-cache'
          }
        })
      ])

      let fetchedProducts: Product[] = []
      let fetchedBanners: BannerSlide[] = []

      // Handle products response
      if (productsResponse.status === 'fulfilled' && productsResponse.value.ok) {
        const productsData = await productsResponse.value.json()
        if (productsData.success) {
          fetchedProducts = productsData.data || []
        }
      }

      // Handle banners response
      if (bannersResponse.status === 'fulfilled' && bannersResponse.value.ok) {
        const bannersData = await bannersResponse.value.json()
        if (bannersData.success) {
          fetchedBanners = bannersData.data || []
        }
      }

      // Update state
      setProducts(fetchedProducts)
      setBanners(fetchedBanners)
      
      // Cache the data if caching is enabled
      if (enableCache) {
        const dataToCache: HomepageData = {
          products: fetchedProducts,
          banners: fetchedBanners,
          lastFetched: Date.now()
        }
        cache.set(cacheKey, dataToCache)
        console.log('💾 Homepage data cached')
      }

    } catch (err) {
      console.error('Error fetching homepage data:', err)
      const errorMessage = err instanceof Error ? err.message : 'حدث خطأ في تحميل بيانات الصفحة الرئيسية'
      setError(errorMessage)
      
      // If there's an error and we have cached data, use it
      if (enableCache && !initialData) {
        const cachedData = cache.get(cacheKey) as HomepageData | null
        if (cachedData) {
          console.log('⚠️ Using cached homepage data due to fetch error')
          setProducts(cachedData.products)
          setBanners(cachedData.banners)
          setIsFromCache(true)
        }
      }
    } finally {
      setLoading(false)
    }
  }, [enableCache, cacheKey, initialData])

  const refetch = useCallback(async () => {
    await fetchHomepageData(false) // Force fresh fetch
  }, [fetchHomepageData])

  const clearCache = useCallback(() => {
    if (enableCache) {
      cache.remove(cacheKey)
      console.log('🗑️ Homepage cache cleared')
    }
  }, [enableCache, cacheKey])

  // Initial setup
  useEffect(() => {
    if (initialData) {
      // We have SSR data, just cache it
      fetchHomepageData(true)
    } else {
      // No initial data, try to load from cache or fetch
      fetchHomepageData(true)
    }
  }, []) // Only run once on mount

  return {
    products,
    banners,
    loading,
    error,
    refetch,
    clearCache,
    isFromCache
  }
}

// Specialized hook for homepage with proper cache settings
export function useHomepage(initialData?: { products: Product[]; banners: BannerSlide[] }) {
  return useHomepageData({
    enableCache: true,
    cacheKey: 'homepage_data',
    initialData
  })
}
