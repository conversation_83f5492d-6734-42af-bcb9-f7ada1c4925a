import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { getTenantId } from '../../../lib/auth-helpers'
import type { BannerSlide } from '../../../types'

// Secure server-side Supabase client
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

// GET /api/homepage/banners - Get banners for homepage
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    const tenantId = getTenantId(request)

    // Fetch banners - filtered by tenant and active
    const { data: banners, error } = await supabaseAdmin
      .from('banner_slides')
      .select('*')
      .eq('tenant_id', tenantId)
      .eq('active', true)
      .order('order_index', { ascending: true })

    if (error) {
      throw error
    }

    // Transform banners to match expected format
    const transformedBanners: BannerSlide[] = (banners || []).map(banner => ({
      id: banner.id,
      title: banner.title,
      subtitle: banner.subtitle,
      image: banner.image,
      linkType: banner.link_type as "product" | "collection" | "custom" | "none",
      linkValue: banner.link_value,
      active: banner.active,
      order: banner.order_index
    }))

    return NextResponse.json({
      success: true,
      data: transformedBanners
    })

  } catch (error) {
    console.error('Homepage banners API error:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في تحميل البانرات'
    }, { status: 500 })
  }
}
