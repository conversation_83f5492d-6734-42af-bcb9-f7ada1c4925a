/**
 * Auth Error Handler
 * Handles common Supabase authentication errors with retry logic
 */

import { AuthError } from '@supabase/supabase-js'

export interface AuthErrorInfo {
  isRetryable: boolean
  shouldSignOut: boolean
  userMessage: string
  retryDelay?: number
}

export function handleAuthError(error: any): AuthErrorInfo {
  const errorMessage = error?.message || error?.toString() || 'Unknown error'
  
  // Handle specific Supabase auth errors
  if (error instanceof AuthError || errorMessage.includes('AuthApiError')) {
    
    // Token refresh errors
    if (errorMessage.includes('Invalid Refresh Token') || 
        errorMessage.includes('Already Used') ||
        errorMessage.includes('refresh_token_not_found')) {
      return {
        isRetryable: false,
        shouldSignOut: true,
        userMessage: 'انتهت صلاحية جلسة العمل. يرجى تسجيل الدخول مرة أخرى.',
        retryDelay: 0
      }
    }
    
    // Network or temporary errors
    if (errorMessage.includes('Failed to fetch') ||
        errorMessage.includes('Network Error') ||
        errorMessage.includes('timeout')) {
      return {
        isRetryable: true,
        shouldSignOut: false,
        userMessage: 'مشكلة في الاتصال. جاري إعادة المحاولة...',
        retryDelay: 2000
      }
    }
    
    // Rate limiting
    if (errorMessage.includes('rate limit') || 
        errorMessage.includes('too many requests')) {
      return {
        isRetryable: true,
        shouldSignOut: false,
        userMessage: 'تم تجاوز الحد المسموح من المحاولات. يرجى الانتظار قليلاً.',
        retryDelay: 5000
      }
    }
    
    // Invalid credentials
    if (errorMessage.includes('Invalid login credentials') ||
        errorMessage.includes('Email not confirmed') ||
        errorMessage.includes('Invalid email or password')) {
      return {
        isRetryable: false,
        shouldSignOut: false,
        userMessage: 'بيانات تسجيل الدخول غير صحيحة.',
        retryDelay: 0
      }
    }
  }
  
  // Default handling for unknown errors
  return {
    isRetryable: false,
    shouldSignOut: false,
    userMessage: 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.',
    retryDelay: 0
  }
}

/**
 * Retry wrapper for auth operations
 */
export async function retryAuthOperation<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> {
  let lastError: any
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await operation()
    } catch (error) {
      lastError = error
      const errorInfo = handleAuthError(error)
      
      // Don't retry if it's not retryable or if this is the last attempt
      if (!errorInfo.isRetryable || attempt === maxRetries) {
        throw error
      }
      
      // Wait before retrying with exponential backoff
      const delay = errorInfo.retryDelay || (baseDelay * Math.pow(2, attempt))
      console.log(`🔄 Auth operation failed, retrying in ${delay}ms (attempt ${attempt + 1}/${maxRetries})`)
      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }
  
  throw lastError
}

/**
 * Safe auth operation wrapper that handles errors gracefully
 */
export async function safeAuthOperation<T>(
  operation: () => Promise<T>,
  fallbackValue?: T,
  onError?: (error: AuthErrorInfo) => void
): Promise<T | undefined> {
  try {
    return await retryAuthOperation(operation)
  } catch (error) {
    const errorInfo = handleAuthError(error)
    console.error('Auth operation failed:', error)
    
    if (onError) {
      onError(errorInfo)
    }
    
    return fallbackValue
  }
}
